// Import the functions you need from the Firebase SDKs
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getFirestore } from "firebase/firestore";
import { getAuth, RecaptchaVerifier } from "firebase/auth";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDnYYpCfdokqLy9vP7MCnR-MwVG6d34dMc",
  authDomain: "credit-chakra-in.firebaseapp.com",
  projectId: "credit-chakra-in",
  storageBucket: "credit-chakra-in.appspot.com",
  messagingSenderId: "20491936969",
  appId: "1:20491936969:web:2e555f7891588558105918",
  measurementId: "G-HQQ12R73G6",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Analytics
let analytics;
if (typeof window !== "undefined") {
  analytics = getAnalytics(app); // Analytics only works in the browser
}

// Initialize Firestore
const db = getFirestore(app);

// Initialize Firebase Authentication
const auth = getAuth(app);

// Set up RecaptchaVerifier globally
const setupRecaptchaVerifier = () => {
  if (!window.recaptchaVerifier) {

    window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
      'size': 'normal',
      'callback': (response) => {
        // reCAPTCHA solved, allow signInWithPhoneNumber.
        // ...
      },
      'expired-callback': () => {
        // Response expired. Ask user to solve reCAPTCHA again.
        // ...
      }
    });
  }
};
export const firestore = getFirestore(app);
export { app, db, analytics, auth, setupRecaptchaVerifier };
