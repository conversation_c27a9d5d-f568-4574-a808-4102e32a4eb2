import React, { useEffect } from 'react';

const ZohoDialog = ({
  open,
  setOpen,
  zohoQuery,
  setZohoQuery,
  importFromZoho,
  isLoading,
}) => {
  // Handle ESC key press
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setZohoQuery('');
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [open, setOpen, setZohoQuery]);

  // Handle click outside
  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      setZohoQuery('');
      setOpen(false);
    }
  };

  if (!open) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 mx-4">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Import from Zoho
          </h2>
          <p className="mt-4 text-gray-600">
            Search for an invoice in your Zoho account
          </p>
        </div>

        {/* Content */}
        <div className="space-y-4">
          <input
            type="text"
            placeholder="Enter invoice number or customer name"
            value={zohoQuery}
            onChange={(e) => setZohoQuery(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          
          {isLoading && (
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Searching...</span>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-8 flex justify-end space-x-3">
          <button
            onClick={() => {
              setZohoQuery('');
              setOpen(false);
            }}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 
              rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            onClick={importFromZoho}
            disabled={!zohoQuery.trim() || isLoading}
            className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md 
              ${(!zohoQuery.trim() || isLoading)
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
              }`}
          >
            Import
          </button>
        </div>
      </div>
    </div>
  );
};

export default ZohoDialog;