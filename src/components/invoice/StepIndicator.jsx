import React from 'react';
import { CheckCircle2 } from 'lucide-react';

const StepIndicator = ({ steps, currentStep, progress }) => {
  return (
    <div className="mb-8">
      {/* Step Circles */}
      <div className="flex justify-between mb-2">
        {steps.map((step) => (
          <div
            key={step.id}
            className={`flex flex-col items-center ${
              step.id === currentStep
                ? 'text-blue-600'
                : step.id < currentStep
                ? 'text-blue-600'
                : 'text-gray-400'
            }`}
          >
            {/* Circle with Number or Checkmark */}
            <div 
              className={`
                flex items-center justify-center w-8 h-8 mb-2 
                rounded-full border-2 border-current
                ${step.id <= currentStep ? 'border-blue-600' : 'border-gray-400'}
              `}
            >
              {step.id < currentStep ? (
                <CheckCircle2 className="w-4 h-4" />
              ) : (
                <span>{step.id}</span>
              )}
            </div>
            
            {/* Step Title */}
            <span className="text-xs text-center max-w-[80px]">{step.title}</span>
          </div>
        ))}
      </div>

      {/* Custom Progress Bar */}
      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
        <div
          className="h-full bg-blue-600 transition-all duration-300 ease-in-out rounded-full"
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
};

export default StepIndicator;