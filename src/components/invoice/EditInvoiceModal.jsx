import React, { useState, useEffect } from "react";

const EditInvoiceModal = ({
    open,
    setOpen,
    extractedInvoiceData,
    onSave,
    isLoading,
  }) => {
    const [formData, setFormData] = useState({});
    const [errors, setErrors] = useState({});
  
    const mandatoryKeywords = ["Phone", "Contact", "GST", "Tax ID"];
    const phoneRegex = /^(\+91|91)?[6-9][0-9]{9}$/;
  
    useEffect(() => {
      if (extractedInvoiceData) {
        const initialFormData = Object.entries(extractedInvoiceData).reduce(
          (acc, [key, data]) => {
            if (key === "_metadata") return acc; // Skip metadata
            acc[key] = data.value; // Extract the value
            return acc;
          },
          {}
        );
        setFormData(initialFormData);
      }
    }, [extractedInvoiceData]);
  
    const validateForm = () => {
      const newErrors = {};
      Object.keys(formData).forEach((field) => {
        const value = formData[field]?.trim();
        if (isMandatory(field) && !value) {
          newErrors[field] = "This field is required";
        } else if (isPhoneField(field) && !phoneRegex.test(value)) {
          newErrors[field] = "Invalid phone number. Must be 10 digits.";
        }
      });
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };
  
    const isMandatory = (key) => {
      const label = formatLabel(key).toLowerCase();
      return mandatoryKeywords.some((keyword) =>
        label.includes(keyword.toLowerCase())
      );
    };
  
    const isPhoneField = (key) => {
      const label = formatLabel(key).toLowerCase();
      return ["phone", "contact"].some((keyword) =>
        label.includes(keyword.toLowerCase())
      );
    };
  
    const formatLabel = (key) => {
      return key.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
    };
  
    const getFeedbackColor = (confidence, isValid) => {
      if (confidence > 0.7 && isValid) return "bg-green-50 text-green-800"; // Green
      if (confidence > 0.4 && isValid) return "bg-yellow-50 text-yellow-800"; // Yellow
      return "bg-red-50 text-red-800"; // Red
    };
    const handlePhoneChange = (key, value) => {
      let cleanedValue = value.replace(/\s+/g, "").trim(); // Remove ALL spaces
    
      // Ensure country code is formatted properly (e.g., +91 or 91)
      if (cleanedValue.startsWith("+91")) {
        cleanedValue = "+91" + cleanedValue.slice(3); // Keep +91, remove extra spaces
      } else if (cleanedValue.startsWith("91")) {
        cleanedValue = "+91" + cleanedValue.slice(2); // Convert 91 -> +91
      } else if (/^\d{10}$/.test(cleanedValue)) {
        cleanedValue = "+91" + cleanedValue; // If only 10 digits, prepend +91
      }
    
      // Update state with cleaned phone number
      setFormData((prev) => ({
        ...prev,
        [key]: cleanedValue,
      }));
    
      if (errors[key]) {
        setErrors((prev) => ({
          ...prev,
          [key]: undefined,
        }));
      }
    };
    
    
  
    const handleSubmit = (e) => {
      e.preventDefault();
      if (!validateForm()) return;
  
      const updatedData = Object.entries(extractedInvoiceData).reduce(
        (acc, [key, originalData]) => {
          if (key === "_metadata") {
            acc[key] = originalData; // Preserve metadata
          } else {
            acc[key] = {
              ...originalData,
              value: formData[key] || "", // Update the value
            };
          }
          return acc;
        },
        {}
      );
  
      onSave(updatedData);
      setOpen(false); // Close the modal
    };
  
    if (!open || !extractedInvoiceData) return null;
  
    const fields = Object.entries(extractedInvoiceData).filter(
      ([key, data]) => key !== "_metadata" && data?.value !== undefined
    );
  
    const fieldPairs = [];
    for (let i = 0; i < fields.length; i += 2) {
      fieldPairs.push([fields[i], fields[i + 1] ? fields[i + 1] : null]);
    }
  
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl flex flex-col">
          {/* Modal Header */}
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Edit Invoice</h2>
          </div>
  
          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto p-6" style={{ maxHeight: "70vh" }}>
            <form onSubmit={handleSubmit} className="space-y-4">
              {fieldPairs.map((pair, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {pair.map((field) => {
                    if (!field) return null;
                    const [key, data] = field;
                    const confidencePercent = Math.round(data.confidence * 100);
                    const value = formData[key]?.trim();
                    const isValid = isPhoneField(key)
                      ? phoneRegex.test(value)
                      : !!value;
  
                    return (
                      <div key={key} className="space-y-1">
                        <div className="flex justify-between items-center">
                          <label
                            className={`block text-sm font-medium ${
                              isMandatory(key) ? "text-gray-900" : "text-gray-700"
                            } capitalize`}
                          >
                            {formatLabel(key)}
                          </label>
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              confidencePercent > 0
                                ? getFeedbackColor(data.confidence, isValid)
                                : "bg-gray-50 text-gray-600"
                            }`}
                          >
                            {isValid
                              ? confidencePercent > 70
                                ? "No Need to Change"
                                : "Needs Review"
                              : "Needs Attention"}
                          </span>
                        </div>
                        <div>
                          <input
                            type="text"
                            value={formData[key] || ""}
                            onChange={(e) => {
                              if (isPhoneField(key)) {
                                handlePhoneChange(key, e.target.value);
                              } else {
                                setFormData((prev) => ({
                                  ...prev,
                                  [key]: e.target.value,
                                }));
                                if (errors[key]) {
                                  setErrors((prev) => ({
                                    ...prev,
                                    [key]: undefined,
                                  }));
                                }
                              }
                            }}
                            className={`w-full px-3 py-2 border rounded-md ${
                              getFeedbackColor(data.confidence, isValid).split(
                                " "
                              )[0]
                            } ${
                              errors[key]
                                ? "border-red-500"
                                : "border-gray-300"
                            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                            placeholder={`Enter ${formatLabel(key)}`}
                          />
                          {errors[key] && (
                            <p className="mt-1 text-xs text-red-600">
                              {errors[key]}
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ))}
            </form>
          </div>
  
          {/* Modal Footer */}
          <div className="p-6 border-t border-gray-200 flex justify-center space-x-3">
            <button
              type="button"
              onClick={() => setOpen(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 
                rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 
                focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={isLoading}
              className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md ${
                isLoading
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              }`}
            >
              {isLoading ? "Processing..." : "Proceed"}
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  export default EditInvoiceModal;
  