import React, { useEffect, useState } from "react";

const PhoneDialog = ({
  open,
  setOpen,
  verificationCode,
  setVerificationCode,
  extractedPhone,
  confirmationResult,
  onSuccess, // ✅ Callback to send verification status back
}) => {
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === "Escape") {
        resetDialogState();
      }
    };

    if (open) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [open]);

  const resetDialogState = () => {
    setVerificationCode("");
    setError("");
    setIsVerifying(false);
    setOpen(false);
  };

  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      resetDialogState();
    }
  };

  const handleVerifyOTP = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError("Please enter a valid 6-digit verification code.");
      return;
    }

    setIsVerifying(true);
    setError("");

    try {
      await confirmationResult.confirm(verificationCode);
      // alert("Phone number verified successfully! ✅");
      resetDialogState();
      onSuccess(true); // ✅ Send verification status back to parent
    } catch (err) {
      console.error("OTP Verification Failed:", err);
      setError("Invalid verification code. Please try again.");
      setIsVerifying(false);
    }
  };

  if (!open) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 mx-4">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Verify Phone Number</h2>
          <p className="mt-4 text-gray-600">Enter the verification code sent to {extractedPhone}</p>
        </div>

        <div className="space-y-4">
          <input
            type="text"
            placeholder="Enter 6-digit code"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            maxLength={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {error && <p className="text-red-600 text-sm">{error}</p>}
        </div>

        <div className="mt-8 flex justify-end space-x-3">
          <button
            onClick={resetDialogState}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            onClick={handleVerifyOTP}
            disabled={isVerifying}
            className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md ${
              isVerifying ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-700"
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
          >
            {isVerifying ? "Verifying..." : "Verify"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PhoneDialog;
