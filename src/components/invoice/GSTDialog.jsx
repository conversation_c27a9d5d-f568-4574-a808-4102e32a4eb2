import React, { useEffect } from "react";

const GSTDialog = ({ open, setOpen, gstDetails,setCurrentStep,extractedInvoiceData }) => {
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === "Escape") {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [open, setOpen]);

  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      setOpen(false);
    }
  };

  if (!open) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 mx-4">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            GST Verification Details
          </h2>
          <p className="mt-4 text-gray-600">
            Below are the details fetched from the GST database.
          </p>
        </div>

        {/* Content */}
        <div className="space-y-4">
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="font-medium">GSTIN: {gstDetails?.gstin}</p>
            <p>Trade Name: {gstDetails?.tradeNam || "N/A"}</p>
            <p>Legal Name: {gstDetails?.lgnm || "N/A"}</p>
            <p>Business Type: {gstDetails?.ctb || "N/A"}</p>
            <p>State Jurisdiction: {gstDetails?.stj || "N/A"}</p>
            <p>Status: {gstDetails?.sts || "N/A"}</p>
          </div>
          <p className="text-sm text-gray-500">
            Data fetched from GST database.
          </p>
        </div>

        {/* Footer */}
        <div className="mt-8 flex justify-end space-x-3">
          <button
            onClick={() => {
              setOpen(false)
              setCurrentStep(4)
            }}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default GSTDialog;
