import React, { useEffect, useState } from 'react';
import { Send, CheckCircle2, ArrowLeft, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { getFirestore, doc, setDoc, getDoc } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";

const StepConfirmation = ({ 
  isLoading, 
  extractedInvoiceData, 
  sellerGstData, 
  buyerGstData, 
  handleBack,
  selectedFile 
}) => {
  const navigate = useNavigate();
  const auth = getAuth();
  const db = getFirestore();
  const storage = getStorage();

  const [isProcessing, setIsProcessing] = useState(false);
  const applicationId = `app-${Date.now()}`;
  const invoiceId = `inv-${Date.now()}`;
  
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        navigate('/login');
      }
    });
    return () => unsubscribe();
  }, [auth, navigate]);

  const extractPhoneNumber = (invoiceData, phoneKeys) => {
    for (const key of phoneKeys) {
      if (invoiceData[key]?.value) {
        return invoiceData[key].value.replace(/\D/g, "").slice(-10);
      }
    }
    return null;
  };

  const buyerPhoneKeys = ["buyer_phone", "buyer_contact", "receiver_phone", "customer_phone", "contact_phone", "customer_contact_number"];
  const sellerPhoneKeys = ["seller_phone", "seller_contact", "supplier_phone", "vendor_phone", "merchant_phone", "supplier_contact_number"];

  const buyerPhone = extractPhoneNumber(extractedInvoiceData, buyerPhoneKeys);
  const sellerPhone = extractPhoneNumber(extractedInvoiceData, sellerPhoneKeys);

  const handleSubmit = async () => {
    try {
      setIsProcessing(true); // Start loader
      alert("Submitting your application...");
      const user = auth.currentUser;
      
      if (!user) {
        alert("You need to be logged in to submit an application.");
        navigate("/login");
        return;
      }

      if (!sellerPhone || !buyerPhone) {
        alert("❌ Seller or Buyer phone number is missing in the invoice!");
        return;
      }
      
      const applicationRef = doc(db, `applications/${applicationId}`);
      await setDoc(applicationRef, {
        userId: user.uid,
        timestamp: new Date(),
        invoiceDetails: extractedInvoiceData,
        sellerGstDetails: sellerGstData,
        buyerGstDetails: buyerGstData,
        status: "pending",
      });
      
      let filePath = null;
      if (selectedFile) {
        const storageRef = ref(storage, `invoices/${sellerPhone}/${applicationId}.pdf`);
        await uploadBytes(storageRef, selectedFile);
        filePath = await getDownloadURL(storageRef);
        await setDoc(applicationRef, { invoiceLink: filePath }, { merge: true });
        console.log(`✅ Invoice PDF uploaded: ${filePath}`);
      }
      
      const sellerRef = doc(db, `users/${sellerPhone}`);
      const sellerDoc = await getDoc(sellerRef);
      if (!sellerDoc.exists()) {
        await setDoc(sellerRef, { sellerDetails: sellerGstData });
      }

      const buyerRef = doc(db, `users/${buyerPhone}`);
      const buyerDoc = await getDoc(buyerRef);
      if (!buyerDoc.exists()) {
        await setDoc(buyerRef, { buyerDetails: buyerGstData });
      }

      navigate("/dashboard", { state: { message: "Application submitted successfully!" } });

    } catch (error) {
      console.error("❌ Error submitting application:", error);
      alert("Error submitting application. Please try again.");
    } finally {
      setIsProcessing(false); // Stop loader
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Confirmation</h3>
        <p className="text-gray-500 text-sm">Review and confirm the verified information</p>
      </div>

      <div className="p-6 bg-gray-50 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Invoice Details</h3>
        <p><strong>Invoice ID:</strong> {invoiceId}</p>
        <p><strong>Invoice Date:</strong> {extractedInvoiceData?.invoice_date?.value || "N/A"}</p>
        <p><strong>Net Amount:</strong> {extractedInvoiceData?.net_amount?.value || "N/A"}</p>
        <p><strong>Total Amount:</strong> {extractedInvoiceData?.total_amount?.value || "N/A"}</p>
      </div>

      <div className="p-6 bg-gray-50 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">GST Details</h3>
        <p><strong>Seller GST:</strong> {sellerGstData?.gstin || "N/A"}</p>
        <p><strong>Buyer GST:</strong> {buyerGstData?.gstin || "N/A"}</p>
      </div>

      <div className="p-4 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-500 mb-1">Seller & Buyer Phone Numbers</p>
        <div className="flex items-center">
          <span className="font-medium">📞 Seller: {sellerPhone || "N/A"}</span>
          {sellerPhone && <CheckCircle2 className="h-4 w-4 text-green-500 ml-2" />}
        </div>
        <div className="flex items-center mt-2">
          <span className="font-medium">📞 Buyer: {buyerPhone || "N/A"}</span>
          {buyerPhone && <CheckCircle2 className="h-4 w-4 text-green-500 ml-2" />}
        </div>
      </div>

      <div className="flex space-x-4">
        <button 
          onClick={handleBack} 
          className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 flex items-center justify-center"
          disabled={isProcessing}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>

        <button 
          onClick={handleSubmit} 
          disabled={isLoading || isProcessing} 
          className={`w-full px-4 py-2 text-sm font-medium text-white rounded-md flex items-center justify-center 
          ${isProcessing ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Submitting...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" /> Submit Invoice
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default StepConfirmation;
