import React from 'react';
import { FileText, CloudIcon } from 'lucide-react';

const StepUploadInvoice = ({
  isLoading,
  isDragging,
  selectedFile,
  error,
  handleFileSelect,
  handleDragEnter,
  handleDragLeave,
  handleDragOver,
  handleDrop,
  openZohoDialog,
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Upload Invoice</h3>
        {/* <p className="text-gray-500 text-sm">
          Upload your PDF invoice
        </p> */}
      </div>

      {/* Upload Options Grid */}
      <div className="grid">
        {/* Drag & Drop Upload Area */}
        <div
          className={`relative rounded-lg cursor-pointer transition-colors duration-200 
            ${isDragging
              ? 'border-2 border-dashed border-blue-600 bg-blue-50'
              : 'border-2 border-dashed border-gray-200 hover:border-blue-400'
            }`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <button
            className="w-full h-full min-h-[160px] flex flex-col items-center justify-center space-y-2 px-4
              disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => document.getElementById('file-upload').click()}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-sm">Processing...</p>
              </>
            ) : (
              <>
                <FileText className="h-8 w-8 text-blue-600" />
                <p className="text-sm font-medium">
                  {isDragging ? 'Drop your PDF here' : 'Drag & drop your PDF here'}
                </p>
                <p className="text-xs text-gray-500">or click to browse</p>
                {selectedFile && (
                  <p className="text-xs text-blue-600 mt-2">{selectedFile.name}</p>
                )}
              </>
            )}
          </button>
          <input
            id="file-upload"
            type="file"
            accept=".pdf"
            className="hidden"
            onChange={handleFileSelect}
          />
        </div>

        {/* Zoho Import Option */}
        {/* <button
          className={`w-full min-h-[160px] flex flex-col items-center justify-center space-y-2 px-4
            border-2 border-gray-200 rounded-lg transition-colors duration-200
            hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed
            disabled:hover:border-gray-200`}
          onClick={openZohoDialog}
          disabled={isLoading}
        >
          <CloudIcon className="h-8 w-8 text-blue-600" />
          <p className="text-sm font-medium">Import from Zoho</p>
          <p className="text-xs text-gray-500">Connect to your Zoho account</p>
        </button> */}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4">
          <p className="text-red-500 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
};

export default StepUploadInvoice;