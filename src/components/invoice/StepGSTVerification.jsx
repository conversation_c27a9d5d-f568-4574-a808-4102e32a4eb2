import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>he<PERSON>, <PERSON>Left } from "lucide-react";
import searchGSTIN from "./GST/SearchGSTIN";

const possibleGstKeysBuyer = [
  "receiver_GST", "receiver_tax_id", "receiverTaxId", "buyer_taxId",
  "buyer_tax_id", "customer_gst", "customer_tax_id"
];

const possibleGstKeysSeller = [
  "supplier_GST", "supplier_tax_id", "supplierTaxId", "seller_taxId",
  "seller_tax_id", "vendor_gst", "vendor_tax_id"
];

const extractGSTNumber = (data, possibleKeys) => {
  if (!data) return "N/A";
  for (const key of possibleKeys) {
    if (data[key]?.value) {
      console.log("GST Number found:", data[key].value);
      return data[key].value; // Extract value from nested structure
    }
  }
  return "N/A"; // Fallback if no GST number is found
};

const StepGSTVerification = ({ 
  isLoading, 
  extractedInvoiceData, 
  setCurrentStep, 
  handleGSTVerificationComplete,
  handleBack,
  savedSellerGstData,
  savedBuyerGstData
}) => {
  const [gstDetailsSeller, setGstDetailsSeller] = useState(savedSellerGstData || null);
  const [gstDetailsBuyer, setGstDetailsBuyer] = useState(savedBuyerGstData || null);
  console.log("GST Details Seller:", gstDetailsSeller);
  console.log("GST Details Buyer:", gstDetailsBuyer);
  const [loading, setLoading] = useState(false);

// Local states for extracted GST numbers
const [extractedBuyerGST, setExtractedBuyerGST] = useState("N/A");
const [extractedSellerGST, setExtractedSellerGST] = useState("N/A");

  useEffect(() => {
    // If saved data exists, use it instead of fetching again
    if (savedSellerGstData) setGstDetailsSeller(savedSellerGstData);
    if (savedBuyerGstData) setGstDetailsBuyer(savedBuyerGstData);
     // Extract GST numbers when `extractedInvoiceData` updates
     const buyerGST = extractGSTNumber(extractedInvoiceData, possibleGstKeysBuyer);
     const sellerGST = extractGSTNumber(extractedInvoiceData, possibleGstKeysSeller);
 
     setExtractedBuyerGST(buyerGST);
     setExtractedSellerGST(sellerGST);
  }, [extractedInvoiceData,savedSellerGstData, savedBuyerGstData]);

  // const initiateGSTVerification = async () => {
  //   if (gstDetailsSeller && gstDetailsBuyer) {
  //     return; // If data already exists, no need to verify again
  //   }

  //   setLoading(true);
  //   try {
  //     // Simulated API Response
  //     const responseBuyer = {
  //       "code": 200,
  //       "data": {
  //         "data": {
  //           "gstin": extractedBuyerGST,
  //           "tradeNam": "Buyer Business Pvt Ltd",
  //           "lgnm": "Buyer Corporation",
  //           "ctb": "Private Limited",
  //           "stj": "Delhi Zone",
  //           "sts": "Active"
  //         }
  //       }
  //     };

  //     const responseSeller = {
  //       "code": 200,
  //       "data": {
  //         "data": {
  //           "gstin": extractedSellerGST,
  //           "tradeNam": "Seller Exports Ltd",
  //           "lgnm": "Seller Corporation",
  //           "ctb": "Foreign LLP",
  //           "stj": "Mumbai Zone",
  //           "sts": "Provisional"
  //         }
  //       }
  //     };

  //     setGstDetailsBuyer(responseBuyer.data.data);
  //     setGstDetailsSeller(responseSeller.data.data);

  //     // Pass verified data to parent component
  //     handleGSTVerificationComplete(responseSeller.data.data, responseBuyer.data.data);

  //   } catch (error) {
  //     console.error("GST Verification Failed", error);
  //     alert("Error fetching GST details.");
  //   }
  //   setLoading(false);
  // };

  const initiateGSTVerification = async () => {
    if (gstDetailsSeller && gstDetailsBuyer) {
      return; // If data already exists, no need to verify again
    }
  
    setLoading(true);
    try {
      console.log("Initiating GST Verification...");
    
      // Fetch Buyer GST Details
      const responseBuyer = await searchGSTIN(extractedBuyerGST); // Call the API function
      console.log("Buyer GST Details:", responseBuyer.data);
      if (responseBuyer) {
        setGstDetailsBuyer(responseBuyer);
      } else {
        console.warn("No Buyer GST details found");
      }
  
      // Fetch Seller GST Details
      const responseSeller = await searchGSTIN(extractedSellerGST); // Call the API function
      console.log("Seller GST Details:", responseSeller.data);
      if (responseSeller) {
        setGstDetailsSeller(responseSeller);
      } else {
        console.warn("No Seller GST details found");
      }
  
      // Pass verified data to parent component
      handleGSTVerificationComplete(
        responseSeller || {},
        responseBuyer || {}
      );
    } catch (error) {
      console.error("GST Verification Failed", error);
      alert("Error fetching GST details.");
    }
    setLoading(false);
  };
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Verify GST Numbers</h3>
        <p className="text-gray-500 text-sm">
          Verify both **Seller** and **Buyer** GST numbers from the invoice.
        </p>
      </div>

      {/* GST Information Card */}
      <div className="p-6 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div>
            <p className="text-sm text-gray-500">Extracted Buyer GST</p>
            <p className="text-lg font-medium">{extractedBuyerGST}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Extracted Seller GST</p>
            <p className="text-lg font-medium">{extractedSellerGST}</p>
          </div>

          <button
            onClick={initiateGSTVerification}
            disabled={isLoading || loading || (gstDetailsSeller && gstDetailsBuyer)}
            className={`inline-flex items-center px-4 py-2 text-sm font-medium text-white 
              bg-blue-600 rounded-md min-w-[120px]
              ${(isLoading || loading || (gstDetailsSeller && gstDetailsBuyer))
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              }`}
          >
            <FileCheck className="h-4 w-4 mr-2" />
            {loading ? "Verifying..." : "Verify GST"}
          </button>
        </div>
      </div>

      {/* Display GST Verification Results */}
      {(gstDetailsBuyer || gstDetailsSeller) && (
        <div className="p-6 bg-gray-50 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4">GST Verification Results</h3>

          

          {/* Buyer Information */}
          {gstDetailsBuyer && (
            <div>
              <h4 className="text-md font-semibold">🔹 Buyer Details</h4>
              <p><strong>GSTIN:</strong> {extractedBuyerGST}</p>
              <p><strong>Trade Name:</strong> {gstDetailsBuyer.tradeNam || "N/A"}</p>
              <p><strong>Legal Name:</strong> {gstDetailsBuyer.lgnm || "N/A"}</p>
              <p><strong>Business Type:</strong> {gstDetailsBuyer.ctb || "N/A"}</p>
              <p><strong>State Jurisdiction:</strong> {gstDetailsBuyer.stj || "N/A"}</p>
              <p><strong>Status:</strong> {gstDetailsBuyer.sts || "N/A"}</p>
            </div>
          )}


          <br/>

          {/* Seller Information */}
          {gstDetailsSeller && (
            <div className="mb-6">
              <h4 className="text-md font-semibold">🔹 Seller Details</h4>
              <p><strong>GSTIN:</strong> {extractedSellerGST}</p>
              <p><strong>Trade Name:</strong> {gstDetailsSeller.tradeNam || "N/A"}</p>
              <p><strong>Legal Name:</strong> {gstDetailsSeller.lgnm || "N/A"}</p>
              <p><strong>Business Type:</strong> {gstDetailsSeller.ctb || "N/A"}</p>
              <p><strong>State Jurisdiction:</strong> {gstDetailsSeller.stj || "N/A"}</p>
              <p><strong>Status:</strong> {gstDetailsSeller.sts || "N/A"}</p>
            </div>
          )}
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        {/* Back Button */}
        <button
          onClick={() => handleBack(2)}
          className="px-6 py-3 text-gray-700 bg-gray-200 rounded-md font-medium flex items-center hover:bg-gray-300"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>

        {/* Next Button */}
        <button
          onClick={() => setCurrentStep(4)}
          disabled={!gstDetailsSeller || !gstDetailsBuyer}
          className={`px-6 py-3 text-white bg-green-600 rounded-md font-medium 
            ${(!gstDetailsSeller || !gstDetailsBuyer) ? "opacity-50 cursor-not-allowed" : "hover:bg-green-700"}`}
        >
          Next Step
        </button>
      </div>
    </div>
  );
};

export default StepGSTVerification;
