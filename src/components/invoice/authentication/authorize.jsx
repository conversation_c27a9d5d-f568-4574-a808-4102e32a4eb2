import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_KEY } from '../../../constants/APIConstants';
const authorize = async (requestToken) => {
  const url = 'https://api.sandbox.co.in/authorize';
  console.log('Authorizing token:', requestToken);
  try {
    // Retrieve the current JWT token from AsyncStorage
    const jwtToken = await AsyncStorage.getItem('access_token');
    console.log('Current JWT Token:', jwtToken);
    if (!jwtToken) {
      throw new Error('JWT token not found. Please authenticate first.');
    }

    const headers = {
      'authorization': `Bearer ${jwtToken}`,
      'x-api-key': API_KEY,
      'x-api-version': '1.0', // Replace with the correct version if needed
      'accept': 'application/json',
    };

    const data = {
      request_token: requestToken, // Replace with the token to be refreshed
    };

    const response = await axios.post(url, data, { headers });
    const refreshedToken = response.data.token;

    // Save refreshed token to AsyncStorage
    await AsyncStorage.setItem('access_token', refreshedToken);
    console.log('Refreshed Token saved to AsyncStorage:', refreshedToken);

    return refreshedToken;
  } catch (error) {
    console.error('Error authorizing:', error);
    throw error;
  }
};

export default authorize;
