import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { API_KEY, API_SECRET } from "../../../constants/APIConstants";

const authenticate = async () => {
  console.log("Authenticating...");

  // Use the proxy prefix if you have set up a proxy (e.g., /api)
  const url = "/api/authenticate"; // Proxy will redirect to `https://api.sandbox.co.in/authenticate`

  const headers = {
    "x-api-key": API_KEY, // Your API Key
    "x-api-secret": API_SECRET, // Your API Secret
    "x-api-version": "1.0", // API version
    accept: "application/json",
  };

  try {
    console.log("Sending authentication request...");

    const response = await axios.post(url, {}, { headers });

    // Validate the response and extract the access token
    if (response.status === 200 && response.data?.access_token) {
      const accessToken = response.data.access_token;

      console.log("Access Token received:", accessToken);

      // Save access token to AsyncStorage
      await AsyncStorage.setItem("access_token", accessToken);
      console.log("Access Token saved to AsyncStorage:", accessToken);

      return accessToken;
    } else {
      throw new Error(
        `Unexpected response: ${response.status} - ${response.statusText}`
      );
    }
  } catch (error) {
    // Enhanced error handling
    if (error.response) {
      console.error(
        "API Response Error:",
        error.response.status,
        error.response.data
      );
    } else if (error.request) {
      console.error("No response received:", error.request);
    } else {
      console.error("Error setting up request:", error.message);
    }

    throw new Error(
      "Authentication failed. Please check your credentials or API configuration."
    );
  }
};

export default authenticate;
