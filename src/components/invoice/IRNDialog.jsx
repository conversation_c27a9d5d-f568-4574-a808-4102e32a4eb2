import React, { useEffect } from 'react';

const IRNDialog = ({ open, setOpen, irnStatus, isProcessing, onGenerate }) => {
  // Handle ESC key press
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [open, setOpen]);

  // Handle click outside
  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      setOpen(false);
    }
  };

  if (!open) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 mx-4">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {irnStatus?.exists ? 'IRN Details' : 'Generate IRN'}
          </h2>
        </div>

        {/* Content */}
        <div className="space-y-4">
          {irnStatus?.exists ? (
            <>
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">IRN Number</p>
                <p className="font-medium">{irnStatus.irn}</p>
              </div>
              <p className="text-sm text-green-600 flex items-center">
                <svg 
                  className="w-4 h-4 mr-1" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M5 13l4 4L19 7" 
                  />
                </svg>
                IRN has been verified
              </p>
            </>
          ) : (
            <>
              <p className="text-gray-600">
                No IRN exists for this invoice. Would you like to generate one?
              </p>
              {isProcessing && (
                <div className="flex items-center justify-center p-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-3 text-gray-600">Generating IRN...</span>
                </div>
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="mt-8 flex justify-start space-x-3">
          <button
            onClick={() => setOpen(false)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
          {!irnStatus?.exists && (
            <button
              onClick={onGenerate}
              disabled={isProcessing}
              className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md 
                ${isProcessing 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                }`}
            >
              Generate IRN
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default IRNDialog;