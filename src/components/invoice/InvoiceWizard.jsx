import React, { useState, useCallback } from 'react';
import { ArrowLeft } from 'lucide-react';

import StepIndicator from './StepIndicator';
import StepUploadInvoice from './StepUploadInvoice';
import StepPhoneVerification from './StepPhoneVerification';
import StepGSTVerification from './StepGSTVerification';
import StepConfirmation from './StepConfirmation';
import PhoneDialog from './PhoneDialog';
import GSTDialog from './GSTDialog';
import IRNDialog from './IRNDialog';
import ZohoDialog from './ZohoDialog';
import EditInvoiceModal from './EditInvoiceModal';

const steps = [
  { id: 1, title: 'Upload Invoice' },
  { id: 2, title: 'Verify Phone' },
  { id: 3, title: 'Verify GST' },
  { id: 4, title: 'Confirm' },
];

const InvoiceWizard = () => {
  // Main wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedFile, setSelectedFile] = useState(null);
  const [sellerGstData, setSellerGstData] = useState(null); // ✅ Fix: Define sellerGstData
  const [buyerGstData, setBuyerGstData] = useState(null);   // ✅ Fix: Define buyerGstData
  const [filePath, setFilePath] = useState(null); // ✅ Track uploaded file path

  const [extractedInvoiceData, setextractedInvoiceData] = useState(null);
  const [verificationStatus, setVerificationStatus] = useState({
    phone: null,
    gst: null,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Dialog states
  const [isPhoneDialogOpen, setIsPhoneDialogOpen] = useState(false);
  const [isGSTDialogOpen, setIsGSTDialogOpen] = useState(false);
  const [isIRNDialogOpen, setIsIRNDialogOpen] = useState(false);
  const [isZohoDialogOpen, setIsZohoDialogOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  // Additional states
  const [verificationCode, setVerificationCode] = useState('');
  const [zohoQuery, setZohoQuery] = useState('');
  const [irnStatus, setIrnStatus] = useState(null);
  const [resendCooldown, setResendCooldown] = useState(0);

  const progress = ((currentStep - 1) / (steps.length - 1)) * 100;

  const handleFinalConfirmation = (uploadedFilePath) => {
    setFilePath(uploadedFilePath); // ✅ Store file path from StepConfirmation
  };
  
  
  // --- Utility Functions ---
  const validateFile = (file) => {
    if (!file) return false;
    if (file.type !== 'application/pdf') {
      setError('Please select a PDF file');
      return false;
    }
    setError('');
    return true;
  };
 // Function to update GST Data
 const handleGSTVerificationComplete = (sellerData, buyerData) => {
  setSellerGstData(sellerData);
  setBuyerGstData(buyerData);
  // setCurrentStep(4); // Move to the confirmation step
};
  const handleFileSelect = async (event) => {
    const file = event.target.files?.[0];
    if (file && validateFile(file)) {
      setIsLoading(true);
      setSelectedFile(file);

      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await fetch('http://localhost:8000/api/data/uploadInvoice', {
          method: 'POST',
          body: formData,
        });
        console.log(response);
        if (!response.ok) {
          throw new Error('Upload failed');
        }

        const data = await response.json();
        setextractedInvoiceData(data);  // This already passes all received data
        setIsEditModalOpen(true);
      } catch (err) {
        setError('Failed to upload file: ' + err.message);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
      const file = e.dataTransfer.files[0];
      if (file && validateFile(file)) {
        handleFileSelect({ target: { files: [file] } });
      }
    },
    [handleFileSelect]
  );

  


  




  // --- Zoho Import ---
  const importFromZoho = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      setIsZohoDialogOpen(false);
      setZohoQuery('');
      setextractedInvoiceData({
        receiverPhone: '+919632776950',
        receiverTaxId: '29AAPFD2064G1ZA',
      });
      setCurrentStep(2);
    } catch (err) {
      setError('Failed to import from Zoho');
    } finally {
      setIsLoading(false);
    }
  };

  // --- Render Step Content ---
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepUploadInvoice
            isLoading={isLoading}
            isDragging={isDragging}
            selectedFile={selectedFile}
            error={error}
            handleFileSelect={handleFileSelect}
            handleDragEnter={handleDragEnter}
            handleDragLeave={handleDragLeave}
            handleDragOver={handleDragOver}
            handleDrop={handleDrop}
            openZohoDialog={() => setIsZohoDialogOpen(true)}
          />
        );
      case 2:
        return (
          <StepPhoneVerification
            isLoading={isLoading}
            extractedInvoiceData={extractedInvoiceData}
            setCurrentStep={setCurrentStep}
          />
        );
      case 3:
        return (
          <StepGSTVerification
            isLoading={isLoading}
            extractedInvoiceData={extractedInvoiceData}
            setCurrentStep={setCurrentStep}
            handleGSTVerificationComplete={handleGSTVerificationComplete}
            handleBack={() => setCurrentStep(2)}
          />
        );
      case 4:
        return (
          <StepConfirmation
          isProcessing={isLoading}
          extractedInvoiceData={extractedInvoiceData}
          sellerGstData={sellerGstData}
          buyerGstData={buyerGstData}
          handleBack={() => setCurrentStep(3)}
          selectedFile={selectedFile} // ✅ Pass PDF file
          handleFinalConfirmation={handleFinalConfirmation} // ✅ Capture file path
        />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      {/* Render dialogs */}
      <PhoneDialog
        open={isPhoneDialogOpen}
        setOpen={setIsPhoneDialogOpen}
        verificationCode={verificationCode}
        setVerificationCode={setVerificationCode}
        extractedPhone={extractedInvoiceData?.receiverPhone}
  
      />

      <GSTDialog
        open={isGSTDialogOpen}
        setOpen={setIsGSTDialogOpen}
        extractedGST={extractedInvoiceData?.receiverTaxId}
       
      />

      <IRNDialog
        open={isIRNDialogOpen}
        setOpen={setIsIRNDialogOpen}
        irnStatus={irnStatus}
        isProcessing={isProcessing}
      />

      <ZohoDialog
        open={isZohoDialogOpen}
        setOpen={setIsZohoDialogOpen}
        zohoQuery={zohoQuery}
        setZohoQuery={setZohoQuery}
        importFromZoho={importFromZoho}
        isLoading={isLoading}
      />

      <EditInvoiceModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        extractedInvoiceData={extractedInvoiceData}
        isLoading={isLoading}
        onSave={(updatedData) => {
          setextractedInvoiceData(updatedData);
          setIsEditModalOpen(false);
          setCurrentStep(2);
        }}
      />

      {/* Main Card */}
      <div className="bg-white rounded-lg shadow-lg">
        <div className="p-6">
          <StepIndicator 
            steps={steps} 
            currentStep={currentStep} 
            progress={progress} 
          />

          {renderStepContent()}

          {error && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

       
        </div>
      </div>
    </div>
  );
};

export default InvoiceWizard;