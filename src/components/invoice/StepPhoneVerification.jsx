import React, { useState,useEffect } from "react";
import PhoneDialog from "./PhoneDialog";
import { Phone } from "lucide-react";
import { signInWithPhoneNumber, RecaptchaVerifier } from "firebase/auth";
import { auth } from "../../firebase";
import { use } from "react";

const StepPhoneVerification = ({isLoading, extractedInvoiceData, setCurrentStep }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [selectedPhone, setSelectedPhone] = useState("");
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [confirmationResult, setConfirmationResult] = useState(null);
  const [isVerified, setIsVerified] = useState(false);
  const  user =auth.currentUser;
  const getPhoneField = () => {
    // const phoneKeywords = ["buyer_phone", "buyer_contact", "receiver_phone", "customer_phone", "contact_phone", "supplier_phone"];
    const phoneKeywords = ["seller_phone", "seller_contact", "supplier_phone", "vendor_phone", "merchant_phone", "supplier_contact_number"];

    for (const [key, value] of Object.entries(extractedInvoiceData || {})) {
      if (phoneKeywords.some((keyword) => key.toLowerCase().includes(keyword.toLowerCase()))) {
        return { key, ...value };
      }
    }
    return null;
  };

  useEffect(() => {
    if(user){
    setIsVerified(true);
    setCurrentStep(3);
    }

  }, []);


  const handleVerifyPhone = (phone) => {
    setSelectedPhone(phone);
    setIsSendingOTP(true);

    window.recaptchaVerifier = new RecaptchaVerifier(auth, "recaptcha-container", {
      size: "invisible",
      callback: (response) => {
        console.log("reCAPTCHA verified:", response);
      },
    });

    signInWithPhoneNumber(auth, phone, window.recaptchaVerifier)
      .then((result) => {
        setIsSendingOTP(false);
        setOtpSent(true);
        setIsDialogOpen(true);
        setConfirmationResult(result);
      })
      .catch((error) => {
        setIsSendingOTP(false);
        console.error("Error sending OTP:", error.message);
        alert("Failed to send OTP. Please try again.");
      });
  };

  const handleSuccess = (status) => {
    setIsVerified(status);
    if (status) {
      setCurrentStep(3);
    }
  };

  const phoneData = getPhoneField();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Verify Phone Number</h3>
        <p className="text-gray-500 text-sm">Verify the extracted phone number from your invoice</p>
      </div>

      <div className="p-6 bg-gray-50 rounded-lg">
        {phoneData ? (
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div>
              <p className="text-sm text-gray-500 capitalize">{phoneData.key.replace(/_/g, " ")}</p>
              <p className="text-lg font-medium">{phoneData.value}</p>
            </div>

            {isVerified ? (
              <p className="text-green-600 font-semibold">Verified ✅</p>
            ) : (
              <button
                onClick={() => handleVerifyPhone(phoneData.value)}
                disabled={isSendingOTP}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md"
              >
                {isSendingOTP ? "Sending..." : "Verify Phone"}
              </button>
            )}
          </div>
        ) : (
          <p className="text-red-500">No phone number found in extracted data.</p>
        )}
      </div>

      {otpSent && selectedPhone && (
        <PhoneDialog
          open={isDialogOpen}
          setOpen={setIsDialogOpen}
          verificationCode={verificationCode}
          setVerificationCode={setVerificationCode}
          extractedPhone={selectedPhone}
          confirmationResult={confirmationResult}
          onSuccess={handleSuccess}
        />
      )}

      <div id="recaptcha-container"></div>
    </div>
  );
};

export default StepPhoneVerification;