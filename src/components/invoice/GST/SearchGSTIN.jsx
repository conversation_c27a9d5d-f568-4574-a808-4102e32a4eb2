import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { jwtDecode } from "jwt-decode";
import { API_KEY } from "../../../constants/APIConstants";
import authenticate from "../authentication/authenticate"; // Ensure authenticate function is implemented and imported
import { firestore } from "../../../firebase"; // Firestore instance
import { doc, setDoc } from "firebase/firestore"; // Firestore functions
import { getAuth } from "firebase/auth";

const isTokenExpired = (token) => {
  if (!token) return true; // No token means it’s expired or not set

  try {
    console.log("Decoding token:", token);
    const decoded = jwtDecode(token);
    console.log("Decoded token:", decoded);
    const currentTime = Date.now() / 1000; // Convert to seconds
    return decoded.exp < currentTime; // Check if token is expired
  } catch (error) {
    console.error("Error decoding token:", error);
    return true; // If decoding fails, consider token expired
  }
};

const SearchGSTIN = async (gstin) => {
  const url = "/api/gst/compliance/public/gstin/search"; // Proxy prefix
  console.log("Searching GSTIN:", gstin);

  try {
    // Retrieve the JWT token from AsyncStorage
    let jwtToken = await AsyncStorage.getItem("access_token");
    console.log("Current JWT Token:", jwtToken);

    // Check if the token is expired or not
    if (!jwtToken || isTokenExpired(jwtToken)) {
      console.log("Token expired or not set. Fetching new token...");
      jwtToken = await authenticate(); // Fetch a new token
      await AsyncStorage.setItem("access_token", jwtToken); // Store new token
    }

    const headers = {
      authorization: `${jwtToken}`, // JWT access token
      "x-api-key": API_KEY, // API Key
      "x-api-version": "1.0", // API version
      accept: "application/json",
      "x-accept-cache": "true",
      "content-type": "application/json",
    };

    console.log("Headers:", headers);

    const data = { gstin }; // Body containing GSTIN

    // Make the POST request
    const response = await axios.post(url, data, { headers });
    console.log("GSTIN Search Response:", response.data);

    // Validate response and return the API response
    if (response.status === 200 && response.data?.data) {
      const gstData = response.data.data.data;
      console.log("GSTIN Search Data:", gstData);

      // Store the GST data in Firestore under `gst/gstin/{gstin}`
      await saveGSTDataToFirestore(gstin, gstData);

      return gstData;
    } else {
      throw new Error(
        `Unexpected response: ${response.status} - ${response.statusText}`
      );
    }
  } catch (error) {
    // Log detailed error for debugging
    if (error.response) {
      console.error(
        "API Response Error:",
        error.response.status,
        error.response.data
      );
    } else if (error.request) {
      console.error("No response received:", error.request);
    } else {
      console.error("Error setting up request:", error.message);
    }

    // Throw an informative error
    throw new Error(
      "Failed to search GSTIN. Please check your API configuration or try again later."
    );
  }
};

/**
 * Function to store GST data in Firestore under `gst/gstin/{gstin}`
 */

const saveGSTDataToFirestore = async (gstin, gstData) => {
  try {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error("User is not authenticated. Cannot save GST data.");
      throw new Error("User must be logged in to save GST data.");
    }

    const gstRef = doc(firestore, `gst/${gstin}`); // Dynamically store under 'gst/{gstin}'
    await setDoc(gstRef, gstData, { merge: true }); // Merge prevents overwriting existing fields
    console.log(`GST Data successfully saved in Firestore for GSTIN: ${gstin}`);
  } catch (error) {
    console.error("Error saving GST data to Firestore:", error);
    throw new Error("Failed to save GST data in Firestore.");
  }
};


export default SearchGSTIN;
