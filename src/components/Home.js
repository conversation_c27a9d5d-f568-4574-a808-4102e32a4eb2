import React from 'react';
import { Link } from 'react-router-dom';
import heroImage1 from '../images/hero-image1.png';
const HomePage = () => {
    return (
        <div>
            {/* Hero Section */}
            <section className="hero-section py-20 bg-dark-teal text-white">
                <div className="container mx-auto px-6">
                    <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
                        <div className="w-full lg:w-1/2">
                            <h1 className="text-4xl md:text-5xl font-bold mb-6">
                                Smarter Lending Starts After Disbursement
                            </h1>
                            <p className="text-xl mb-8">
                                Credit Chakra helps lenders monitor post-loan behavior, catch risk early, and engage MSMEs with data-driven interventions.
                            </p>
                            <Link to="/products" className="btn btn-primary py-3 px-6 rounded-lg bg-yellow-500 text-dark-teal font-semibold hover:bg-yellow-600 transition duration-300">
                                Explore Our Solutions
                            </Link>
                        </div>
                        <div className="w-full lg:w-1/2 flex justify-center">
                            <img
                                src={heroImage1}
                                alt="MSME Growth"
                                className="rounded-lg shadow-xl max-w-full h-auto"
                                width="500"
                                height="500"
                            />
                        </div>
                    </div>
                </div>
            </section>

            {/* Why Choose Credit Chakra Section */}
            <section className="py-16 bg-light-blue">
                <div className="container mx-auto px-6">
                    <h2 className="text-3xl font-bold mb-12 text-center text-dark-teal">Why Choose Credit Chakra?</h2>
                    <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                        <ChallengeCard
                            title="Post-Disbursement Visibility Gap"
                            description="Loan sanctioned. Funds disbursed. But then? Lenders are often left in the dark — unsure if the money was used correctly or if the business is under stress."
                            solution="Credit Chakra brings post-sanction visibility into focus."
                        />
                        <ChallengeCard
                            title="Early Warning System Missing"
                            description="By the time EMI delays or NPAs show up in your system, it's already too late. We help you catch red flags earlier — like drop in GST filings, UPI inactivity, or missing bank inflows."
                            solution="Our AI-powered monitoring detects stress signals weeks in advance."
                        />
                        <ChallengeCard
                            title="Communication Breakdown"
                            description="Missed calls. No response. Lack of context. Borrowers go silent when you need them most."
                            solution="Our nudge layer sends smart, timely messages that open communication — before it turns into a recovery case."
                        />
                        <ChallengeCard
                            title="Fragmented Team Operations"
                            description="Credit, risk, collections — all using disconnected systems. Your teams are overloaded with fragmented tools."
                            solution="We unify these layers with a real-time risk dashboard, health scores, and workflow-ready alerts your teams can act on."
                        />
                        <ChallengeCard
                            title="Compliance & Regulatory Requirements"
                            description="From Account Aggregator and GSTN integration to secure data flows and audit trails, modern lending demands robust compliance infrastructure."
                            solution="We've designed Credit Chakra for modern, RBI-aligned lending from the ground up."
                        />
                    </div>
                    <div className="mt-12 text-center">
                        <Link to="/about" className="btn btn-primary py-3 px-6 rounded-lg bg-yellow-500 text-dark-teal font-semibold hover:bg-yellow-600 transition duration-300">
                            Learn More About Us
                        </Link>
                    </div>
                </div>
            </section>


            {/* What We Offer Section */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-6">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold mb-4 text-dark-teal">What We Offer</h2>
                        <p className="text-xl text-gray-600">A modern post-sanction control layer for MSME lenders.</p>
                    </div>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <ProductCard
                            iconPath="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            title="Live Credit Monitoring Engine"
                            description="Track fund use and borrower activity using GST, AA, and UPI data."
                        />
                        <ProductCard
                            iconPath="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                            title="Borrower Health Score & Risk Triggers"
                            description="Get early alerts for distress, misuse, or declining business performance."
                        />
                        <ProductCard
                            iconPath="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                            title="Smart Nudge & Engagement Layer"
                            description="Send timely, RBI-compliant messages to re-engage borrowers at risk."
                        />
                        <ProductCard
                            iconPath="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            title="Portfolio Risk Dashboard"
                            description="Unify credit, risk, and recovery teams with real-time borrower insights."
                        />
                        <ProductCard
                            iconPath="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                            title="Consent & Data Infrastructure"
                            description="Secure, modular APIs for AA, GSTN, UPI, and core banking integration."
                        />
                    </div>
                    <div className="mt-12 text-center">
                        <Link to="/products" className="btn btn-primary py-3 px-6 rounded-lg bg-yellow-500 text-dark-teal font-semibold hover:bg-yellow-600 transition duration-300">
                            View All Products
                        </Link>
                    </div>
                </div>
            </section>

            {/* Who We Work With Section */}
            <section className="py-16 bg-light-blue">
                <div className="container mx-auto px-6">
                    <h2 className="text-3xl font-bold mb-12 text-center text-dark-teal">Who We Work With</h2>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <ClientCard title="Public Sector Banks (PSBs)" />
                        <ClientCard title="NBFCs & Microfinance Institutions" />
                        <ClientCard title="Fintech Lenders" />
                        <ClientCard title="Co-operative Banks & SFBs" />
                        <ClientCard title="Development Finance Institutions (DFIs)" />
                    </div>
                    <div className="mt-12 text-center">
                        <Link to="/contact" className="btn btn-primary py-3 px-6 rounded-lg bg-yellow-500 text-dark-teal font-semibold hover:bg-yellow-600 transition duration-300">
                            Partner With Us
                        </Link>
                    </div>
                </div>
            </section>

            {/* Built by Lenders Section */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-6">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold mb-6 text-dark-teal">Built by Lenders, for Lenders</h2>
                        <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                            Credit Chakra is founded by fintech builders and ex-lending professionals who've seen the gap between disbursement and default — and know how to close it.
                        </p>
                        <p className="text-xl text-gray-600 max-w-4xl mx-auto mt-4">
                            We're on a mission to make lending smarter, empathetic, and truly inclusive.
                        </p>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Link to="/team" className="btn btn-primary py-3 px-6 rounded-lg bg-dark-teal text-white font-semibold hover:bg-teal transition duration-300">
                            Meet the Team
                        </Link>
                        <Link to="/resources" className="btn btn-secondary py-3 px-6 rounded-lg border-2 border-dark-teal text-dark-teal font-semibold hover:bg-dark-teal hover:text-white transition duration-300">
                            Download Product Deck
                        </Link>
                    </div>
                </div>
            </section>

            {/* Let's Talk Section */}
            <section className="py-16 bg-dark-teal text-white">
                <div className="container mx-auto px-6">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold mb-6">Let's Talk</h2>
                        <p className="text-xl max-w-3xl mx-auto">
                            Whether you're a bank, NBFC, or policy innovator — we'd love to hear from you.
                        </p>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Link to="/demo" className="btn btn-primary py-3 px-6 rounded-lg bg-yellow-500 text-dark-teal font-semibold hover:bg-yellow-600 transition duration-300">
                            Book a Demo
                        </Link>
                        <Link to="/contact" className="btn btn-secondary py-3 px-6 rounded-lg border-2 border-white text-white font-semibold hover:bg-white hover:text-dark-teal transition duration-300">
                            Contact Us
                        </Link>
                    </div>
                </div>
            </section>
        </div>
    );
};

// ChallengeCard Component
const ChallengeCard = ({ title, description, solution }) => (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100">
        <h3 className="text-xl font-semibold mb-4 text-dark-teal">{title}</h3>
        <p className="text-gray-700 mb-4 leading-relaxed">{description}</p>
        <div className="bg-green-50 border-l-4 border-green-500 p-3 rounded">
            <p className="text-green-800 font-semibold">{solution}</p>
        </div>
    </div>
);

// ProductCard Component
const ProductCard = ({ iconPath, title, description }) => (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100">
        <div className="flex items-center mb-4">
            <div className="bg-yellow-500 rounded-full p-3 mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={iconPath} />
                </svg>
            </div>
            <h3 className="text-xl font-semibold text-dark-teal">{title}</h3>
        </div>
        <p className="text-gray-700">{description}</p>
    </div>
);

// ClientCard Component
const ClientCard = ({ title }) => (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-center border border-gray-100">
        <div className="w-12 h-12 bg-dark-teal rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
        </div>
        <h3 className="text-lg font-semibold text-dark-teal">{title}</h3>
    </div>
);

export default HomePage;