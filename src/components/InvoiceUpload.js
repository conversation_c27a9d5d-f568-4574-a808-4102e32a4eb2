import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Input,
  Alert,
  Grid,
  CircularProgress,
  Paper,
  IconButton,
  TextField,
  Container,
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  Send,
  SystemUpdate,
  Description
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled components
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const UploadButton = styled(Button)(({ theme, isDragActive }) => ({
  height: '150px',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
  padding: theme.spacing(2),
  textAlign: 'center',
  transition: 'all 0.3s ease',
  border: isDragActive ? `2px dashed ${theme.palette.primary.main}` : '1px solid rgba(0, 0, 0, 0.23)',
  backgroundColor: isDragActive ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
  '& svg': {
    fontSize: '2rem',
  },
  '&:hover': {
    border: isDragActive 
      ? `2px dashed ${theme.palette.primary.main}`
      : `1px solid ${theme.palette.primary.main}`,
  },
}));

const InvoiceUpload = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedData, setUploadedData] = useState(null);
  const [isDragActive, setIsDragActive] = useState(false);

  // Helper function to format the field label
  const formatLabel = (key) => {
    return key
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getValue = (value) => {
    if (!value) return '';
    if (typeof value === 'string') return value;
    if (value.value) return value.value;
    if (Array.isArray(value)) {
      return value.map(item => item.value || '').join(', ');
    }
    return '';
  };

  const getConfidence = (value) => {
    if (!value || typeof value === 'string') return null;
    if (value.confidence) return value.confidence;
    if (Array.isArray(value)) {
      return value.map(item => item.confidence || 0).reduce((a, b) => a + b, 0) / value.length;
    }
    return null;
  };

  const validateFile = (file) => {
    if (!file) return false;
    if (file.type !== 'application/pdf') {
      setError('Please select a PDF file');
      return false;
    }
    setError('');
    return true;
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && validateFile(file)) {
      setSelectedFile(file);
    }
  };

  // Drag and drop handlers
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);

    const file = e.dataTransfer.files[0];
    if (file && validateFile(file)) {
      setSelectedFile(file);
    }
  }, []);

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file first');
      return;
    }

    setIsLoading(true);
    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const response = await fetch('http://localhost:8000/api/data/uploadInvoice', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const data = await response.json();
      setUploadedData(data);
      setError('');
    } catch (err) {
      setError('Failed to upload file: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = () => {
    setSelectedFile(null);
    setUploadedData(null);
    setError('');
  };

  const getDisplayableFields = (data) => {
    if (!data) return {};
    const { _metadata, ...fields } = data;
    return fields;
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Card elevation={3}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h5" component="h1" gutterBottom>
            Upload Invoice
          </Typography>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            Upload your invoice to process and extract information
          </Typography>

          <Box sx={{ mt: 4 }}>
            <Grid container spacing={2}>
              {/* Upload Button with Drag & Drop */}
              <Grid item xs={12} md={6}>
                <Box
                  component="div"
                  onDragEnter={handleDragEnter}
                  onDragLeave={handleDragLeave}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                >
                  <UploadButton
                    variant="outlined"
                    component="label"
                    fullWidth
                    isDragActive={isDragActive}
                  >
                    <CloudUpload />
                    <Typography>
                      {isDragActive 
                        ? 'Drop PDF here'
                        : 'Drop PDF here or click to upload'
                      }
                    </Typography>
                    <VisuallyHiddenInput
                      type="file"
                      accept=".pdf"
                      onChange={handleFileSelect}
                    />
                  </UploadButton>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <UploadButton
                  variant="outlined"
                  fullWidth
                >
                  <SystemUpdate />
                  <Typography>Import from Zoho</Typography>
                </UploadButton>
              </Grid>
            </Grid>

            {/* Selected File Display */}
            {selectedFile && (
              <Box sx={{ mt: 3, space: 2 }}>
                <Paper
                  sx={{
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    bgcolor: 'grey.50',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Description color="primary" />
                    <Typography variant="body2">
                      {selectedFile.name}
                    </Typography>
                  </Box>
                  <IconButton
                    onClick={handleDelete}
                    disabled={isLoading}
                    color="error"
                    size="small"
                  >
                    <Delete />
                  </IconButton>
                </Paper>

                <Button
                  variant="contained"
                  fullWidth
                  onClick={handleUpload}
                  disabled={isLoading}
                  sx={{ mt: 2 }}
                  startIcon={isLoading ? <CircularProgress size={20} /> : <Send />}
                >
                  {isLoading ? 'Processing...' : 'Upload Invoice'}
                </Button>
              </Box>
            )}

            {/* Error Display */}
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            {/* Extracted Data Display */}
            {uploadedData && (
              <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>
                  Extracted Information
                </Typography>
                <Grid container spacing={2}>
                  {Object.entries(getDisplayableFields(uploadedData)).map(([key, value]) => {
                    const confidence = getConfidence(value);
                    return (
                      <Grid item xs={12} sm={6} key={key}>
                        <TextField
                          fullWidth
                          label={formatLabel(key)}
                          value={getValue(value)}
                          helperText={confidence ? `Confidence: ${(confidence * 100).toFixed(1)}%` : ''}
                          InputProps={{
                            readOnly: true,
                          }}
                          variant="outlined"
                          size="small"
                        />
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default InvoiceUpload;