import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc } from 'firebase/firestore';
import { db, auth } from '../firebase'; // Ensure Firebase is properly configured

const getStatusColor = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getRequiredAction = (status) => {
  switch (status) {
    case 'pending':
      return {
        title: 'Steps to Complete',
        description: 'Please submit additional documents for verification.',
        actionItems: [
          'Buyer Confirmation',
          'Provide Bank Statement for last 3 months',
          'Submit Company PAN Card',
          
        ]
      };
    case 'approved':
      return {
        title: 'Application Approved',
        description: 'Your application has been approved. Next steps for disbursement:',
        actionItems: [
          'Complete KYC verification',
          'Sign loan agreement',
          'Provide bank account details'
        ]
      };
    case 'rejected':
      return {
        title: 'Application Rejected',
        description: 'Your application was not approved. You may:',
        actionItems: [
          'Review rejection reasons',
          'Update application with correct information',
          'Contact support for assistance'
        ]
      };
    default:
      return {
        title: 'Status Unknown',
        description: 'Please contact support for assistance.',
        actionItems: []
      };
  }
};

const ApplicationDetails = () => {
  const [application, setApplication] = useState(null);
  const [loading, setLoading] = useState(true);
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchApplication = async () => {
      try {
        setLoading(true);
        
        const user = auth.currentUser;
        if (!user) {
          console.error("User not authenticated");
          navigate('/dashboard', { state: { message: 'User not logged in' }, replace: true });
          return;
        }

        const applicationRef = doc(db, "applications", id);
        const applicationSnap = await getDoc(applicationRef);

        if (applicationSnap.exists()) {
          setApplication({
            id: applicationSnap.id,
            ...applicationSnap.data(),
            timestamp: applicationSnap.data().timestamp.toDate() // Convert Firestore timestamp
          });
        } else {
          navigate('/dashboard', { state: { message: 'Application not found' }, replace: true });
        }
      } catch (error) {
        console.error('Error fetching application:', error);
        navigate('/dashboard', { state: { message: 'Error loading application details' }, replace: true });
      } finally {
        setLoading(false);
      }
    };

    fetchApplication();
  }, [id, navigate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!application) {
    return null;
  }

  const requiredAction = getRequiredAction(application.status);
  const formattedDate = new Date(application.timestamp).toLocaleDateString();
  const formattedAmount = parseFloat(application.invoiceDetails?.total_amount?.value || 0).toLocaleString();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <button
          onClick={() => navigate('/dashboard')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
          Back to Applications
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Application #{application.id}
                </h1>
                <p className="text-sm text-gray-500">
                  Submitted on {formattedDate}
                </p>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(application.status)}`}>
                {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <p className="text-sm text-gray-500">Invoice Amount</p>
                <p className="font-semibold">₹{formattedAmount}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Invoice Date</p>
                <p className="font-semibold">{application.invoiceDetails?.invoice_date?.value || 'N/A'}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Seller Information</h2>
            <p className="text-sm"><strong>Trade Name:</strong> {application.sellerGstDetails?.tradeNam || 'N/A'}</p>
            <p className="text-sm"><strong>GSTIN:</strong> {application.sellerGstDetails?.gstin || 'N/A'}</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold mb-4">Buyer Information</h2>
            <p className="text-sm"><strong>Trade Name:</strong> {application.buyerGstDetails?.tradeNam || 'N/A'}</p>
            <p className="text-sm"><strong>GSTIN:</strong> {application.buyerGstDetails?.gstin || 'N/A'}</p>
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-6">
            <h2 className="text-lg font-semibold mb-4">{requiredAction.title}</h2>
            <p className="text-sm text-gray-600 mb-4">{requiredAction.description}</p>
            <ul className="list-disc ml-6 text-sm">
              {requiredAction.actionItems.map((item, index) => (
                <li key={index} className="mb-2">{item}</li>
              ))}
            </ul>
            <button
              onClick={() => navigate('/dashboard', { state: { message: 'Action completed successfully' } })}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors mt-4"
            >
              Complete Actions
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationDetails;
