import React from 'react';
import { Link } from 'react-router-dom';

const Products = () => {
    const products = [
        {
            id: 1,
            title: 'Borrower Consent & Data Infrastructure',
            description: 'Secure, RBI-compliant data foundation that powers all Credit Chakra solutions with real-time financial insights.',
            details: [
                'GST integration with real-time invoice tracking and revenue analysis',
                'Account Aggregator ready consent flows with live bank data fetch',
                'UPI transaction monitoring and intelligent categorization',
                'Bank statement ingestion with automated cash flow analysis',
                'Role-based access control for RM, risk, and recovery teams',
                'API-first architecture for seamless lender integration',
                'Data privacy compliance with encryption and audit trails'
            ],
            link: '/products/data-infrastructure',
            icon: 'M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4'
        },
        {
            id: 2,
            title: 'Live Credit Monitoring Engine',
            description: 'Continuous 24/7 borrower health tracking with AI-powered early warning systems to prevent defaults before they happen.',
            details: [
                'Real-time GST return analysis and revenue trend monitoring',
                'UPI transaction pattern analysis for cash flow insights',
                'Account Aggregator data for comprehensive financial health',
                'Automated alerts for fund diversion, revenue drops, and inactivity',
                'Customizable risk thresholds based on borrower profiles',
                'Integration with existing loan management systems',
                'Mobile-first dashboards for field teams and relationship managers'
            ],
            link: '/products/monitoring-engine',
            icon: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
        },
        {
            id: 3,
            title: 'Borrower Health Score & Risk Triggers',
            description: 'Dynamic AI-driven scoring system that combines financial and behavioral data to predict and prevent NPAs.',
            details: [
                'Machine learning models trained on 10M+ MSME data points',
                'Real-time score updates based on transaction patterns',
                'Early NPA prediction with 85%+ accuracy up to 90 days ahead',
                'Behavioral indicators: payment delays, fund usage patterns',
                'Visual risk bands with actionable intervention recommendations',
                'Integration with collection workflows and field operations',
                'Customizable scoring parameters for different loan products'
            ],
            link: '/products/health-score',
            icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
        },
        {
            id: 4,
            title: 'Smart Nudge & Engagement Layer',
            description: 'Proactive borrower engagement platform that prevents defaults through timely, personalized interventions.',
            details: [
                'Behavioral nudge library with 50+ proven intervention templates',
                'Multi-channel delivery: WhatsApp, SMS, Email, and Voice calls',
                'AI-powered message personalization in 12+ Indian languages',
                'Automated trigger system based on real-time risk signals',
                'RBI-compliant communication templates and consent management',
                'A/B testing framework for optimizing engagement effectiveness',
                'Integration with CRM systems and collection workflows'
            ],
            link: '/products/smart-nudge',
            icon: 'M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z'
        },
        {
            id: 5,
            title: 'Portfolio Risk Intelligence Dashboard',
            description: 'Comprehensive analytics platform providing 360-degree portfolio insights for proactive risk management.',
            details: [
                'Real-time portfolio health monitoring across all loan products',
                'Drill-down capabilities from portfolio → segment → individual borrower',
                'Automated account tagging for restructuring and field follow-up',
                'Predictive analytics for portfolio stress testing',
                'Configurable views by vintage, geography, product type, and risk bands',
                'Executive dashboards with KPI tracking and trend analysis',
                'Export capabilities for regulatory reporting and board presentations'
            ],
            link: '/products/portfolio-dashboard',
            icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
        },
        {
            id: 6,
            title: 'AI Co-Pilot for Credit Teams',
            description: 'Next-generation GPT-powered assistant that transforms credit operations with intelligent automation and insights.',
            details: [
                'Natural language interface for complex portfolio queries',
                'Automated borrower health summaries and risk assessments',
                'Intelligent recommendation engine for intervention strategies',
                'Auto-draft personalized nudges and collection messages',
                'Integration with all Credit Chakra modules for unified insights',
                'Voice-enabled mobile app for field teams and relationship managers',
                'Continuous learning from user interactions and outcomes'
            ],
            link: '/products/ai-copilot',
            icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z',
            badge: 'Coming Soon'
        },
        {
            id: 7,
            title: 'Credit Chakra API Suite',
            description: 'Developer-friendly APIs that enable seamless integration of Credit Chakra\'s intelligence into existing systems.',
            details: [
                'RESTful APIs with comprehensive documentation and SDKs',
                'Real-time webhooks for instant risk alerts and score updates',
                'Sandbox environment for testing and development',
                'Rate limiting and authentication for enterprise security',
                'Custom integration support for legacy systems',
                'White-label solutions for fintech partners',
                '99.9% uptime SLA with 24/7 technical support'
            ],
            link: '/products/api-suite',
            icon: 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4'
        }
    ];

    return (
        <section className="py-20 bg-gradient-to-br from-light-blue to-white">
            <div className="container mx-auto px-6">
                <div className="text-center mb-16">
                    <h1 className="text-5xl font-bold mb-6 text-dark-teal">Our Product Suite</h1>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Comprehensive post-disbursement solutions that transform how lenders monitor, engage, and manage their MSME portfolios
                    </p>
                </div>
                <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8">
                    {products.map((product) => (
                        <div key={product.id} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                            <div className="relative">
                                {product.badge && (
                                    <div className="absolute -top-4 -right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                        {product.badge}
                                    </div>
                                )}
                                <div className="flex items-start mb-6">
                                    <div className="bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl p-4 mr-4 shadow-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={product.icon} />
                                        </svg>
                                    </div>
                                    <div className="flex-1">
                                        <h2 className="text-2xl font-bold text-dark-teal mb-2 leading-tight">{product.title}</h2>
                                    </div>
                                </div>
                                <p className="mb-6 text-gray-700 leading-relaxed">{product.description}</p>
                                <div className="mb-8">
                                    <h3 className="text-lg font-semibold text-dark-teal mb-3">Key Features:</h3>
                                    <ul className="space-y-2">
                                        {product.details.map((detail, i) => (
                                            <li key={i} className="flex items-start">
                                                <div className="bg-green-100 rounded-full p-1 mr-3 mt-1 flex-shrink-0">
                                                    <svg className="h-3 w-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                    </svg>
                                                </div>
                                                <span className="text-gray-700 text-sm leading-relaxed">{detail}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                <div className="flex space-x-3">
                                    <Link
                                        to={product.link}
                                        className="flex-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-dark-teal font-bold py-3 px-6 rounded-lg hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 text-center shadow-lg hover:shadow-xl transform hover:scale-105"
                                    >
                                        Learn More
                                    </Link>
                                    <button className="bg-dark-teal text-white font-semibold py-3 px-4 rounded-lg hover:bg-teal transition-all duration-300 shadow-lg">
                                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
                <div className="text-center mt-16">
                    <div className="bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto border border-gray-100">
                        <h3 className="text-3xl font-bold text-dark-teal mb-4">Ready to Transform Your Credit Operations?</h3>
                        <p className="text-gray-600 mb-6 text-lg">
                            Join 100+ leading lenders who trust Credit Chakra to reduce NPAs and improve borrower relationships
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link
                                to="/demo"
                                className="bg-gradient-to-r from-dark-teal to-teal text-white font-bold py-4 px-8 rounded-lg hover:from-teal hover:to-dark-teal transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                            >
                                Schedule a Demo
                            </Link>
                            <Link
                                to="/contact"
                                className="bg-white border-2 border-dark-teal text-dark-teal font-bold py-4 px-8 rounded-lg hover:bg-dark-teal hover:text-white transition-all duration-300"
                            >
                                Contact Sales
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Products;
