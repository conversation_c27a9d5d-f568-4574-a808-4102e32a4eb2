import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db, auth } from '../firebase'; // Ensure Firebase is properly configured

const StatCard = ({ title, value, icon, className }) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
    <div className="flex items-center">
      <div className="p-3 rounded-full bg-blue-100 mr-4">
        {icon}
      </div>
      <div>
        <p className="text-sm text-gray-600">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
      </div>
    </div>
  </div>
);

const CustomAlert = ({ children, onClose }) => (
  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 relative">
    <div className="flex">
      <div className="flex-shrink-0">
        <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      </div>
      <div className="ml-3">
        <p className="text-sm text-green-800">{children}</p>
      </div>
      {onClose && (
        <button onClick={onClose} className="ml-auto pl-3 text-green-400 hover:text-green-500">
          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  </div>
);

const Dashboard = () => {
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        setLoading(true);
        
        const user = auth.currentUser;
        if (!user) {
          console.error("User not authenticated");
          return;
        }

        const q = query(collection(db, "applications"), where("userId", "==", user.uid));
        const querySnapshot = await getDocs(q);

        const applicationsData = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp.toDate() // Convert Firestore timestamp
        }));

        setApplications(applicationsData);
      } catch (error) {
        console.error("Error fetching applications:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, []);

  const getStatistics = () => {
    const total = applications.length;
    const pending = applications.filter(app => app.status === 'pending').length;
    const approved = applications.filter(app => app.status === 'approved').length;
    const totalAmount = applications.reduce((sum, app) => 
      sum + parseFloat(app.invoiceDetails?.total_amount?.value || 0), 0
    );

    return { total, pending, approved, totalAmount };
  };

  const stats = getStatistics();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {location.state?.message && (
        <CustomAlert onClose={() => navigate(location.pathname, { replace: true, state: {} })}>
          {location.state.message}
        </CustomAlert>
      )}

      {/* Statistics Section - Restored */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard title="Total Applications" value={stats.total} icon="📄" />
        <StatCard title="Pending Applications" value={stats.pending} icon="⏳" />
        <StatCard title="Approved Applications" value={stats.approved} icon="✅" />
        <StatCard title="Total Amount" value={`₹${stats.totalAmount.toLocaleString()}`} icon="💰" />
      </div>

      <h1 className="text-2xl font-bold mb-6">My Applications</h1>

      {applications.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <p className="mt-4 text-gray-500">No applications found</p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {applications.map((application) => (
            <div key={application.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-semibold">
                    Invoice #{application.invoiceDetails?.invoice_id?.value || 'N/A'}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {new Date(application.timestamp).toLocaleDateString()}
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800`}>
                  {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                </span>
              </div>

              <p className="text-sm"><span className="font-medium">Seller:</span> {application.sellerGstDetails?.tradeNam || 'N/A'}</p>
              <p className="text-sm"><span className="font-medium">Buyer:</span> {application.buyerGstDetails?.tradeNam || 'N/A'}</p>
              <p className="text-sm"><span className="font-medium">Amount:</span> ₹{parseFloat(application.invoiceDetails?.total_amount?.value || 0).toLocaleString()}</p>

              <button
                onClick={() => navigate(`/applications/${application.id}`)}
                className="mt-4 w-full px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors"
              >
                View Details
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
