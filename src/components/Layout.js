// src/components/Layout/Layout.jsx
import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getAuth, onAuthStateChanged, signOut } from 'firebase/auth';
import feather from 'feather-icons';
import { getFirestore } from 'firebase/firestore';
import '../css/styles.css';

const UserMenu = ({ user, onClose }) => {
  const navigate = useNavigate();
  const auth = getAuth();
  
  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
      <div className="px-4 py-2 text-sm text-gray-700 border-b">
        {user.email}
      </div>
      <Link
        to="/profile"
        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        onClick={onClose}
      >
        My Profile
      </Link>
      <Link
        to="/dashboard"
        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        onClick={onClose}
      >
        My Applications
      </Link>
      <button
        onClick={handleLogout}
        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
      >
        Logout
      </button>
    </div>
  );
};

const Header = ({ toggleMobileMenu }) => {
  const [user, setUser] = useState(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef(null);
  const auth = getAuth();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
    });

    return () => unsubscribe();
  }, [auth]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className="py-4 shadow-md bg-white">
      <nav className="container mx-auto px-6 flex justify-between items-center">
        <Link to="/" className="text-2xl font-bold flex items-center">
          <div className="logo-icon bg-dark-teal text-white rounded-full w-12 h-12 flex items-center justify-center mr-2 font-bold text-xl">
            CC
          </div>
          <span className="logo-text text-dark-teal">Credit Chakra</span>
        </Link>
        
        <div className="hidden md:flex space-x-6">
          {['Home', 'About', 'Services', 'Products', 'Contact'].map((text, index) => (
            <Link
              key={index}
              to={text === 'Home' ? '/' : `/${text.toLowerCase()}`}
              className="text-dark-teal hover:text-teal transition duration-300"
            >
              {text}
            </Link>
          ))}
        </div>

        <div className="hidden md:flex items-center space-x-4">
          {user ? (
            <div className="relative" ref={menuRef}>
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="flex items-center space-x-2"
              >
                <div className="w-10 h-10 rounded-full bg-dark-teal text-white flex items-center justify-center">
                  {user.email?.[0].toUpperCase() || 'U'}
                </div>
                <svg
                  className={`w-4 h-4 transition-transform ${isMenuOpen ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {isMenuOpen && (
                <UserMenu user={user} onClose={() => setIsMenuOpen(false)} />
              )}
            </div>
          ) : (
            <Link
              to="/apply"
              className="bg-yellow-500 text-dark-teal font-semibold py-2 px-4 rounded-lg hover:bg-yellow-600 transition duration-300"
            >
              Apply Now
            </Link>
          )}
        </div>

        <button
          aria-label="Open mobile menu"
          className="md:hidden text-dark-teal"
          onClick={toggleMobileMenu}
        >
          <i data-feather="menu"></i>
        </button>
      </nav>
    </header>
  );
};

const MobileMenu = ({ mobileMenuOpen, closeMobileMenu, user }) => (
  <div className={`md:hidden ${mobileMenuOpen ? 'block' : 'hidden'} bg-white`}>
    {['Home', 'About', 'Services', 'Products', 'Contact'].map((text, index) => (
      <Link
        key={index}
        to={text === 'Home' ? '/' : `/${text.toLowerCase()}`}
        className="block px-4 py-2 text-dark-teal hover:bg-gray-100 transition duration-300"
        onClick={closeMobileMenu}
      >
        {text}
      </Link>
    ))}
    {user ? (
      <>
        <Link
          to="/profile"
          className="block px-4 py-2 text-dark-teal hover:bg-gray-100 transition duration-300"
          onClick={closeMobileMenu}
        >
          My Profile
        </Link>
        <Link
          to="/dashboard"
          className="block px-4 py-2 text-dark-teal hover:bg-gray-100 transition duration-300"
          onClick={closeMobileMenu}
        >
          My Applications
        </Link>
        <button
          onClick={async () => {
            await signOut(getAuth());
            closeMobileMenu();
          }}
          className="block w-full text-left px-4 py-2 text-dark-teal hover:bg-gray-100 transition duration-300"
        >
          Logout
        </button>
      </>
    ) : (
      <Link 
        to="/apply" 
        className="block px-4 py-2 bg-yellow-500 text-dark-teal font-semibold hover:bg-yellow-600 transition duration-300"
        onClick={closeMobileMenu}
      >
        Apply Now
      </Link>
    )}
  </div>
);

const Footer = () => (
  <footer className="bg-dark-teal text-white py-12">
    <div className="container mx-auto px-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div>
          <h3 className="text-2xl font-semibold mb-4">Credit Chakra</h3>
          <p className="mb-4">Empowering MSMEs in India with innovative fintech solutions</p>
          <div className="flex space-x-4">
            {['facebook', 'twitter', 'linkedin', 'instagram'].map((icon, index) => (
              <a key={index} href="#" className="hover:text-yellow-500 transition duration-300">
                <i data-feather={icon}></i>
              </a>
            ))}
          </div>
        </div>
        <div>
          <h3 className="text-xl font-semibold mb-4">Quick Links</h3>
          <ul className="space-y-2">
            {['About', 'Products', 'Services', 'Blog', 'Contact'].map((text, index) => (
              <li key={index}>
                <Link to={`/${text.toLowerCase()}`} className="hover:text-yellow-500 transition duration-300">
                  {text}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        <div>
          <h3 className="text-xl font-semibold mb-4">Resources</h3>
          <ul className="space-y-2">
            {[
              { name: 'FAQ', path: '/faq' },
              { name: 'Privacy Policy', path: '/privacy-policy' },
              { name: 'Terms of Service', path: '/terms-of-service' },
              { name: 'Careers', path: '/careers' }
            ].map((resource, index) => (
              <li key={index}>
                <Link to={resource.path} className="hover:text-yellow-500 transition duration-300">
                  {resource.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        <div>
          <h3 className="text-xl font-semibold mb-4">Contact Us</h3>
          <p className="mb-2">
            <i data-feather="map-pin" className="inline-block mr-2"></i>
            403, 8th Main Rd, Raj Mahal Vilas Extension, Armane Nagar, Bengaluru, Karnataka, India, 560080.
          </p>
          <p className="mb-2">
            <i data-feather="phone" className="inline-block mr-2"></i>
            +91 9538731848
          </p>
          <p className="mb-4">
            <i data-feather="mail" className="inline-block mr-2"></i>
            <EMAIL>
          </p>
          <Link to="/apply" className="bg-yellow-500 text-dark-teal font-semibold py-2 px-4 rounded-lg hover:bg-yellow-600 transition duration-300">
            Apply Now
          </Link>
        </div>
      </div>
      <div className="border-t border-teal pt-8 mt-8 text-center">
        <p>&copy; 2024 Credit Chakra. All rights reserved.</p>
      </div>
    </div>
  </footer>
);

const Layout = ({ children }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [user, setUser] = useState(null);
  const location = useLocation();
  const auth = getAuth();

  useEffect(() => {
    feather.replace();
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
    });
    return () => unsubscribe();
  }, [auth]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header toggleMobileMenu={toggleMobileMenu} />
      <MobileMenu 
        mobileMenuOpen={mobileMenuOpen} 
        closeMobileMenu={closeMobileMenu}
        user={user}
      />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
};

export default Layout;