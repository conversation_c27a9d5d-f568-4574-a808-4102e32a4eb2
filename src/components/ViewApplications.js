import React, { useState, useEffect } from 'react';

const ViewApplications = () => {
    // State to hold loan applications data
    const [applications, setApplications] = useState([]);

    // Fetch applications data (this is a placeholder for your API call)
    useEffect(() => {
        // Sample data to simulate fetched loan applications
        const fetchedApplications = [
            {
                contact_name: '<PERSON>',
                email: '<EMAIL>',
                phone: '************',
                business_name: 'JD Enterprises',
                business_type: 'Small Enterprise',
                loan_amount: '500,000 INR',
                loan_purpose: 'Working capital',
                date_created: new Date(),
            },
            {
                contact_name: '<PERSON>',
                email: '<EMAIL>',
                phone: '************',
                business_name: 'Smith & Co.',
                business_type: 'Medium Enterprise',
                loan_amount: '1,000,000 INR',
                loan_purpose: 'Expansion',
                date_created: new Date(),
            },
        ];
        // Replace the above line with your API call and response handling
        setApplications(fetchedApplications);
    }, []);

    return (
        <div className="bg-light-blue min-h-screen">
            <section className="py-16">
                <div className="container mx-auto px-6">
                    <h1 className="text-3xl font-bold mb-6 text-center text-dark-teal">Submitted Loan Applications</h1>

                    {applications.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white rounded-lg shadow-md">
                                <thead>
                                    <tr className="bg-yellow-500 text-dark-teal">
                                        <th className="py-2 px-4 text-left">Contact Name</th>
                                        <th className="py-2 px-4 text-left">Email</th>
                                        <th className="py-2 px-4 text-left">Phone</th>
                                        <th className="py-2 px-4 text-left">Business Name</th>
                                        <th className="py-2 px-4 text-left">Business Type</th>
                                        <th className="py-2 px-4 text-left">Loan Amount</th>
                                        <th className="py-2 px-4 text-left">Loan Purpose</th>
                                        <th className="py-2 px-4 text-left">Date Submitted</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {applications.map((application, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="py-2 px-4">{application.contact_name}</td>
                                            <td className="py-2 px-4">{application.email}</td>
                                            <td className="py-2 px-4">{application.phone || 'N/A'}</td>
                                            <td className="py-2 px-4">{application.business_name}</td>
                                            <td className="py-2 px-4">{application.business_type}</td>
                                            <td className="py-2 px-4">{application.loan_amount}</td>
                                            <td className="py-2 px-4">{application.loan_purpose}</td>
                                            <td className="py-2 px-4">{application.date_created ? application.date_created.toLocaleString() : 'N/A'}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <p className="text-center text-gray-700">No loan applications have been submitted yet.</p>
                    )}
                </div>
            </section>
        </div>
    );
};

export default ViewApplications;
