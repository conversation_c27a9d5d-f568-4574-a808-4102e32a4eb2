import React, { useState, useEffect } from 'react';

const ViewContacts = () => {
    // State to hold contact messages data
    const [contacts, setContacts] = useState([]);

    // Fetch contacts data (this is a placeholder for your API call)
    useEffect(() => {
        // Sample data to simulate fetched contact messages
        const fetchedContacts = [
            {
                name: '<PERSON>',
                email: '<EMAIL>',
                phone: '************',
                subject: 'Inquiry about services',
                message: 'I would like more information about your financial products.',
                date_created: new Date(),
            },
            {
                name: '<PERSON>',
                email: '<EMAIL>',
                phone: '************',
                subject: 'Feedback',
                message: 'I love the services provided. Keep up the good work!',
                date_created: new Date(),
            },
        ];
        // Replace the above line with your API call and response handling
        setContacts(fetchedContacts);
    }, []);

    return (
        <div className="bg-light-blue min-h-screen">
            <section className="py-16">
                <div className="container mx-auto px-6">
                    <h1 className="text-3xl font-bold mb-6 text-center text-dark-teal">Submitted Contact Messages</h1>

                    {contacts.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white rounded-lg shadow-md">
                                <thead>
                                    <tr className="bg-yellow-500 text-dark-teal">
                                        <th className="py-2 px-4 text-left">Name</th>
                                        <th className="py-2 px-4 text-left">Email</th>
                                        <th className="py-2 px-4 text-left">Phone</th>
                                        <th className="py-2 px-4 text-left">Subject</th>
                                        <th className="py-2 px-4 text-left">Message</th>
                                        <th className="py-2 px-4 text-left">Date Submitted</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {contacts.map((contact, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="py-2 px-4">{contact.name}</td>
                                            <td className="py-2 px-4">{contact.email}</td>
                                            <td className="py-2 px-4">{contact.phone || 'N/A'}</td>
                                            <td className="py-2 px-4">{contact.subject}</td>
                                            <td className="py-2 px-4">{contact.message}</td>
                                            <td className="py-2 px-4">{contact.date_created ? contact.date_created.toLocaleString() : 'N/A'}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <p className="text-center text-gray-700">No contact messages have been submitted yet.</p>
                    )}
                </div>
            </section>
        </div>
    );
};

export default ViewContacts;
