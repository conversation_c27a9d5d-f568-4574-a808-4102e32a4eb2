{"name": "credit-chakra-website", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.3.1", "@react-native-async-storage/async-storage": "^1.24.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.9", "feather-icons": "^4.29.2", "firebase": "^11.0.1", "firebase-admin": "^12.7.0", "firebase-functions": "^6.1.0", "http-proxy-middleware": "^3.0.3", "jwt-decode": "^4.0.0", "lucide-react": "^0.469.0", "nodemailer": "^6.9.15", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-router-dom": "^6.27.0", "react-scripts": "5.0.1", "react-toastify": "^10.0.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-google": "^0.14.0"}}