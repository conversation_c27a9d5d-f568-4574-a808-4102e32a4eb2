{"functions": [{"source": "functions", "codebase": "default", "timeoutSeconds": 300, "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "hosting": {"public": "build", "rewrites": [{"source": "**", "destination": "/index.html"}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "storage": {"rules": "storage.rules"}, "emulators": {"functions": {"port": 5002}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}}