/* eslint-disable */
// Import necessary Firebase and Node.js modules
const functions = require("firebase-functions");
const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const { initializeApp } = require("firebase-admin/app");
const nodemailer = require("nodemailer");

// Initialize Firebase Admin SDK
initializeApp();

// Configure Nodemailer using process.env environment variables for enhanced security
const transporter = nodemailer.createTransport({
    service: "gmail",
    auth: {
        user: process.env.EMAIL_USERNAME, // Use environment variables
        pass: process.env.EMAIL_PASSWORD,
    },
});

// Inline HTML for a standardized email signature
const signatureHTML = `
    <div>
        <p style="margin: 0; color: #065f46; font-weight: bold;">Credit Chakra Team</p>
        <p style="margin: 4px 0 0;">Empowering Your Business</p>
        
        <div style="margin-top: 16px; font-size: 0.9rem; color: #555;">
            <p style="margin: 0;">📞 <strong>Phone:</strong> <a href="tel:+************" style="color: #065f46; text-decoration: none;">+91 9538731848</a></p>
            <p style="margin: 0;">📧 <strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #065f46; text-decoration: none;"><EMAIL></a></p>
            <p style="margin: 0;">📍 <strong>Address:</strong> 403, 8th Main Rd, Raj Mahal Vilas Extension, Armane Nagar, Bengaluru, Karnataka, India, 560080.</p>
        </div>

        <div style="margin-top: 16px; color: #888; font-size: 0.8rem;">
            <p style="margin: 0;">Follow us on 
                <a href="https://facebook.com" style="color: #065f46; text-decoration: none;">Facebook</a>, 
                <a href="https://twitter.com" style="color: #065f46; text-decoration: none;">Twitter</a>, and 
                <a href="https://linkedin.com" style="color: #065f46; text-decoration: none;">LinkedIn</a>
            </p>
            <p style="margin: 4px 0 0;">© ${new Date().getFullYear()} Credit Chakra. All rights reserved.</p>
        </div>
    </div>
`;

// Utility function to construct HTML email content with consistent styling
const createEmailTemplate = (recipientName, messageContent) => `
    <p style="font-family: Arial, sans-serif; color: #333;">Hi ${recipientName},</p>
    <div style="font-family: Arial, sans-serif; color: #333; font-size: 1rem; line-height: 1.6;">
        ${messageContent}
    </div>
    <br/>
    <p style="font-family: Arial, sans-serif; color: #333;">Best regards,</p>
    ${signatureHTML}
`;

// Function to handle sending emails
const sendEmail = async (options) => {
    try {
        await transporter.sendMail(options);
        functions.logger.info(`Email sent to ${options.to}`);
    } catch (error) {
        functions.logger.error("Error sending email:", error);
        throw new Error("Failed to send email");
    }
};

// Firestore trigger for "contacts" collection: Sends email to the user and the Credit Chakra team
exports.sendContactEmail = onDocumentCreated({
    document: "/contacts/{docId}",
    region: "asia-south1",
}, async (event) => {
    const data = event.data.data();
    const { email: userEmail, name: userName, message, subject, phone } = data;

    // Define owner email template
    const mailOptionsOwner = {
        from: "<EMAIL>",
        to: process.env.OWNER_EMAIL, // Use environment variable
        subject: `New Contact Message from ${userName}`,
        html: createEmailTemplate(
            "Team",
            `
            <p><strong>Name:</strong> ${userName}</p>
            <p><strong>Email:</strong> ${userEmail}</p>
            <p><strong>Phone:</strong> ${phone}</p>
            <p><strong>Subject:</strong> ${subject}</p>
            <p><strong>Message:</strong> ${message}</p>
            `
        ),
    };

    // Define user confirmation email template
    const mailOptionsUser = {
        from: "<EMAIL>",
        to: userEmail,
        subject: "Thank you for contacting Credit Chakra!",
        html: createEmailTemplate(
            userName,
            `<p>We have received your message and will get back to you soon.</p>
             <p><strong>Your message:</strong> ${message}</p>`
        ),
    };

    try {
        // Send emails to owner and user
        await sendEmail(mailOptionsOwner);
        await sendEmail(mailOptionsUser);
        functions.logger.info("Contact emails sent successfully");
    } catch (error) {
        functions.logger.error("Error in sending contact emails:", error);
    }
});

// Firestore trigger for "applications" collection: Sends application details to the user and Credit Chakra team
exports.sendApplicationEmail = onDocumentCreated({
    document: "/applications/{docId}",
    region: "asia-south1",
}, async (event) => {
    const data = event.data.data();
    const { email: userEmail, businessName, contactName, loanAmount, purpose, businessType, phone } = data;

    // Define owner application email template
    const mailOptionsOwner = {
        from: "<EMAIL>",
        to: process.env.OWNER_EMAIL,
        subject: `New Application from ${contactName}`,
        html: createEmailTemplate(
            "Team",
            `
            <p><strong>Business Name:</strong> ${businessName}</p>
            <p><strong>Contact Name:</strong> ${contactName}</p>
            <p><strong>Email:</strong> ${userEmail}</p>
            <p><strong>Phone:</strong> ${phone}</p>
            <p><strong>Business Type:</strong> ${businessType}</p>
            <p><strong>Loan Amount:</strong> ${loanAmount}</p>
            <p><strong>Purpose:</strong> ${purpose}</p>
            `
        ),
    };

    // Define user confirmation application email template
    const mailOptionsUser = {
        from: "<EMAIL>",
        to: userEmail,
        subject: "Thank you for applying with Credit Chakra!",
        html: createEmailTemplate(
            contactName,
            `<p>We have received your application for a ${businessType} with a loan amount of ${loanAmount} INR. Our team will review your details and get back to you soon.</p>`
        ),
    };

    try {
        // Send emails to owner and user
        await sendEmail(mailOptionsOwner);
        await sendEmail(mailOptionsUser);
        functions.logger.info("Application emails sent successfully");
    } catch (error) {
        functions.logger.error("Error in sending application emails:", error);
    }
});